# Dockerfile.flask
FROM python:3.10-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && \
    apt-get install -y \
        libpq5 \
        libpq-dev \
        python3-dev \
        make \
        docker.io && \
    usermod -aG docker root && \
    rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY superset-llm/requirements.txt ./requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Copy the entire superset-llm directory into the container
COPY superset-llm/ ./

# Create any necessary directories
RUN mkdir -p agents docs routers utils

# Make sure the app.py is executable
RUN chmod +x app.py

EXPOSE 8111

# Update the CMD to use the correct path since files are copied into the image
CMD ["python", "superset-llm/app.py"]